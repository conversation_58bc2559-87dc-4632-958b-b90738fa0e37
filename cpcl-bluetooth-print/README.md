# cpcl-bluetooth-print
适用于微信小程序的蓝牙打印，采用CPCL指令
# 基于CPCL指令集的微信小程序连接蓝牙打印机示例

小程序连接蓝牙打印机打印文本与二维码等示例在 github 上都能找到一些，唯独打印图片这个案例几乎没有。希望能帮助到有打印图片需求的小伙伴。

-   测试打印机：[汉印HM-A300L](https://cn.hprt.com/ChanPin/)

-   打印机指令类型：`CPCL 指令集`

-   注：图片越大打印越慢，由于小程序只能使用低功率蓝牙，一次最多发送 20 个字节的数据，而图片数据可以达到几万、几十万个字节。

## 主要参考

-   [便携式蓝牙打印](https://ask.dcloud.net.cn/article/37984)

-   [微信小程序连接蓝牙打印机示例](https://developers.weixin.qq.com/community/develop/doc/0008acd004ccd86b37d649ee55b009?highLine=%25E8%2593%259D%25E7%2589%2599)

