{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "miniprogramRoot": "src/", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./src/"}], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": ["less"], "useStaticServer": true, "condition": false}, "compileType": "miniprogram", "libVersion": "2.15.0", "appid": "wx2494b493238d5ed4", "projectname": "cpcl-bluetooth-print", "srcMiniprogramRoot": "src/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}