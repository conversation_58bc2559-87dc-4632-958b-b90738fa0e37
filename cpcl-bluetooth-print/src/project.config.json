{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": false, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "compileType": "miniprogram", "libVersion": "2.31.0", "appid": "wx2494b493238d5ed4", "projectname": "cpcl-bluetooth-print", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}