{"condition": {"miniprogram": {"list": [{"name": "pages/print/index", "pathName": "pages/print/index", "query": "", "scene": null}, {"name": "", "pathName": "pages/print/index", "query": "", "launchMode": "default", "scene": null}]}}, "projectname": "cpcl-bluetooth-print", "setting": {"compileHotReLoad": true}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html"}