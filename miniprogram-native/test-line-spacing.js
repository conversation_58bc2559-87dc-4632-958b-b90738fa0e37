/**
 * 行间距修复效果对比测试
 * 对比修复前后的行间距差异
 */

const EnhancedPrinter = require('./utils/printer/enhancedPrinter.js');

console.log('📏 === 行间距修复效果对比测试 ===\n');

// 创建增强打印器实例
const printer = new EnhancedPrinter();

// 测试数据
const testData = {
  sampleId: 'LIMS001',
  sampleType: '水样',
  location: '采样点A',
  date: '2024-01-15',
  items: ['常规检测', '重金属分析'],
  storage: '冷藏',
  status: '待测'
};

console.log('🔧 1. 当前行间距设置分析');
console.log('========================');

// 生成CPCL命令
const commands = printer.generateEnhancedCPCLCommands(testData);
const commandStr = String.fromCharCode(...commands);

// 提取所有TEXT命令的Y坐标
const textCommands = commandStr.match(/TEXT 3 0 8 (\d+)/g);

if (textCommands) {
  const yPositions = textCommands.map(cmd => parseInt(cmd.match(/\d+$/)[0]));
  
  console.log('📐 Y坐标详细分析:');
  yPositions.forEach((y, index) => {
    console.log(`   第${index + 1}行: Y=${y}像素`);
  });
  
  console.log('\n📊 行间距计算:');
  const spacings = [];
  for (let i = 1; i < yPositions.length; i++) {
    const spacing = yPositions[i] - yPositions[i-1];
    spacings.push(spacing);
    console.log(`   第${i}行→第${i+1}行: ${spacing}像素`);
  }
  
  if (spacings.length > 0) {
    const avgSpacing = Math.round(spacings.reduce((a, b) => a + b, 0) / spacings.length);
    console.log(`\n✅ 平均行间距: ${avgSpacing}像素`);
    
    // 对比效果
    console.log('\n📈 修复效果对比:');
    console.log('   修复前: 约32像素 (过于紧密)');
    console.log(`   修复后: ${avgSpacing}像素 (${Math.round((avgSpacing/32-1)*100)}%改善)`);
    
    if (avgSpacing >= 70) {
      console.log('   评价: ✅ 显著改善，行间距适中');
    } else if (avgSpacing >= 50) {
      console.log('   评价: ✅ 有改善，但可以进一步优化');
    } else {
      console.log('   评价: ❌ 改善不明显，需要调整');
    }
  }
}

console.log('\n🎯 2. 实际打印效果预期');
console.log('======================');

console.log('修复前的打印效果:');
console.log('样品编号：LIMS001');
console.log('样品类别：水样');        // 行间距过近
console.log('采样地点：采样点A');      // 内容拥挤
console.log('采样日期：2024-01-15');   // 难以阅读

console.log('\n修复后的打印效果:');
console.log('样品编号：LIMS001');
console.log('');                      // 增加的行间距
console.log('样品类别：水样');
console.log('');                      // 增加的行间距  
console.log('采样地点：采样点A');
console.log('');                      // 增加的行间距
console.log('采样日期：2024-01-15');

console.log('\n🔍 3. 技术参数详情');
console.log('==================');

console.log('CPCL命令参数:');
console.log('   字体: TEXT 3 0 8 (字体3，旋转0度，大小8)');
console.log('   起始Y坐标: 32像素 (yPos=8, 乘以4)');
console.log('   行间距增量: 18个单位 (lineHeight=18)');
console.log('   实际行间距: 72像素 (18*4)');

console.log('\n纸张适配:');
console.log('   纸张宽度: 58mm');
console.log('   每行字符: 32个字符');
console.log('   行间距: 72像素 (约3.6mm)');

console.log('\n🧪 4. 测试建议');
console.log('==============');

console.log('请按以下步骤验证修复效果:');
console.log('1. 在小程序中连接蓝牙打印机');
console.log('2. 点击"核心问题测试"按钮');
console.log('3. 观察打印出的标签行间距');
console.log('4. 对比修复前后的视觉效果');

console.log('\n预期改善:');
console.log('✅ 行与行之间有明显间隔');
console.log('✅ 内容不再拥挤在一起');
console.log('✅ 整体阅读体验显著提升');
console.log('✅ 标签看起来更加专业');

console.log('\n🎉 如果您仍然觉得行间距不够，我可以进一步调整！');

// 生成对比数据
console.log('\n📋 5. 命令对比数据');
console.log('==================');

console.log('生成的CPCL命令长度:', commands.length, '字节');
console.log('包含的文本行数:', textCommands ? textCommands.length : 0);

if (textCommands && textCommands.length > 0) {
  console.log('\n前3行的CPCL命令示例:');
  textCommands.slice(0, 3).forEach((cmd, index) => {
    console.log(`   第${index + 1}行: ${cmd}`);
  });
}

console.log('\n✅ 行间距修复测试完成！');
