/**
 * 测试预览页面检测项目两行显示功能
 */

// 模拟预览页面的检测项目处理逻辑
function testPreviewTwoLines() {
  console.log('=== 预览页面检测项目两行显示测试 ===\n')

  // 测试用例
  const testCases = [
    {
      name: '短文本测试',
      detectionMethod: 'pH值、溶解氧',
      expected: '单行显示'
    },
    {
      name: '中等长度文本测试', 
      detectionMethod: 'pH值、溶解氧、化学需氧量、生化需氧量',
      expected: '可能单行或两行显示'
    },
    {
      name: '长文本测试（有分隔符）',
      detectionMethod: 'pH值、溶解氧、化学需氧量、生化需氧量、氨氮、总磷、总氮、悬浮物',
      expected: '两行显示，在分隔符处分割'
    },
    {
      name: '超长文本测试（无分隔符）',
      detectionMethod: '重金属铅镉汞砷铬镍铜锌检测分析方法原子吸收分光光度法',
      expected: '两行显示，强制分割'
    },
    {
      name: '空内容测试',
      detectionMethod: '',
      expected: '显示默认值'
    },
    {
      name: 'null内容测试',
      detectionMethod: null,
      expected: '显示默认值'
    }
  ]

  testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: ${testCase.name}`)
    console.log(`输入: "${testCase.detectionMethod}"`)
    console.log(`预期: ${testCase.expected}`)
    
    const result = processTestItemsForDisplay(testCase.detectionMethod)
    console.log(`结果:`)
    console.log(`  单行显示: ${result.isSingleLine}`)
    if (result.isSingleLine) {
      console.log(`  内容: "${result.singleLineText}"`)
    } else {
      console.log(`  第一行: "${result.firstLine}"`)
      console.log(`  第二行: "${result.secondLine}"`)
    }
    console.log('---\n')
  })
}

/**
 * 模拟预览页面的检测项目处理逻辑
 */
function processTestItemsForDisplay(testItems) {
  if (!testItems) {
    return {
      isSingleLine: true,
      singleLineText: '常规检测',
      firstLine: '',
      secondLine: ''
    }
  }

  // 计算每行最大字符数（预览页面可以稍微宽松一些）
  const maxCharsPerLine = 22 // 预览页面比打印稍宽
  const labelPrefix = '检测项目：'
  const maxContentChars = maxCharsPerLine - labelPrefix.length

  if (testItems.length <= maxContentChars) {
    // 内容较短，单行显示
    return {
      isSingleLine: true,
      singleLineText: testItems,
      firstLine: '',
      secondLine: ''
    }
  } else {
    // 内容较长，分两行显示
    const splitResult = splitTextIntelligently(testItems, maxCharsPerLine)
    return {
      isSingleLine: false,
      singleLineText: '',
      firstLine: splitResult.firstLine,
      secondLine: splitResult.secondLine
    }
  }
}

/**
 * 智能分割文本
 */
function splitTextIntelligently(text, maxCharsPerLine) {
  const firstLineEnd = Math.min(maxCharsPerLine, text.length)
  let splitPoint = firstLineEnd

  // 尝试在合适的位置分割
  if (text.length > maxCharsPerLine) {
    const separators = ['、', '，', ',', ' ', '；', ';']
    let bestSplit = firstLineEnd

    for (let i = Math.min(maxCharsPerLine - 3, text.length - 3); i >= Math.max(maxCharsPerLine - 8, 0); i--) {
      if (separators.includes(text[i])) {
        bestSplit = i + 1
        break
      }
    }
    splitPoint = bestSplit
  }

  const firstLine = text.substring(0, splitPoint).trim()
  const secondLine = text.substring(splitPoint).trim()

  return { firstLine, secondLine }
}

/**
 * 测试WXML渲染逻辑
 */
function testWXMLRendering() {
  console.log('\n=== WXML渲染逻辑测试 ===\n')
  
  const testCases = [
    {
      name: '单行显示',
      testItemsDisplay: {
        isSingleLine: true,
        singleLineText: 'pH值、溶解氧',
        firstLine: '',
        secondLine: ''
      }
    },
    {
      name: '两行显示（有第二行）',
      testItemsDisplay: {
        isSingleLine: false,
        singleLineText: '',
        firstLine: 'pH值、溶解氧、化学需氧量、',
        secondLine: '生化需氧量、氨氮、总磷'
      }
    },
    {
      name: '两行显示（无第二行）',
      testItemsDisplay: {
        isSingleLine: false,
        singleLineText: '',
        firstLine: 'pH值、溶解氧、化学需氧量',
        secondLine: ''
      }
    }
  ]

  testCases.forEach((testCase, index) => {
    console.log(`WXML测试 ${index + 1}: ${testCase.name}`)
    const display = testCase.testItemsDisplay
    
    if (display.isSingleLine) {
      console.log(`  渲染: <text>检测项目：</text><text>${display.singleLineText}</text>`)
    } else {
      console.log(`  渲染: <text>检测项目：</text>`)
      console.log(`        <text>${display.firstLine}</text>`)
      if (display.secondLine) {
        console.log(`        <text>${display.secondLine}</text>`)
      }
    }
    console.log('---\n')
  })
}

/**
 * 测试字符数计算
 */
function testCharacterCounting() {
  console.log('\n=== 字符数计算测试 ===\n')
  
  const maxCharsPerLine = 22
  const labelPrefix = '检测项目：'
  const maxContentChars = maxCharsPerLine - labelPrefix.length
  
  console.log(`预览页面配置:`)
  console.log(`  每行最大字符数: ${maxCharsPerLine}`)
  console.log(`  标签前缀: "${labelPrefix}" (${labelPrefix.length}字符)`)
  console.log(`  内容最大字符数: ${maxContentChars}`)
  console.log()
  
  const testTexts = [
    'pH值',
    'pH值、溶解氧',
    'pH值、溶解氧、化学需氧量',
    'pH值、溶解氧、化学需氧量、生化需氧量',
    'pH值、溶解氧、化学需氧量、生化需氧量、氨氮'
  ]
  
  testTexts.forEach(text => {
    const willBeSingleLine = text.length <= maxContentChars
    console.log(`"${text}" (${text.length}字符) -> ${willBeSingleLine ? '单行' : '两行'}显示`)
  })
}

// 运行测试
function runAllTests() {
  testPreviewTwoLines()
  testWXMLRendering()
  testCharacterCounting()
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPreviewTwoLines,
    testWXMLRendering,
    testCharacterCounting,
    processTestItemsForDisplay,
    splitTextIntelligently,
    runAllTests
  }
  
  // 如果直接运行此文件，执行测试
  if (require.main === module) {
    runAllTests()
  }
} else {
  // 在浏览器或小程序环境中直接运行
  runAllTests()
}
