<!--pages/sampling/task-list.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
      <input 
        class="search-input" 
        type="text" 
        placeholder="搜索任务..." 
        value="{{searchKeyword}}" 
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view wx:if="{{searchKeyword}}" class="clear-search" bindtap="clearSearch">
        <image src="/static/icons/close.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="filter-btn" bindtap="showFilter">
      <image src="/static/icons/filter.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 筛选标签 -->
  <scroll-view class="filter-tags" scroll-x="true" wx:if="{{filterTags.length > 0}}">
    <view class="tag-list">
      <view 
        wx:for="{{filterTags}}" 
        wx:key="key" 
        class="filter-tag {{item.active ? 'active' : ''}}"
        bindtap="toggleFilterTag"
        data-key="{{item.key}}"
      >
        <text>{{item.label}}</text>
        <image wx:if="{{item.active}}" class="tag-close" src="/static/icons/close-white.png" mode="aspectFit"></image>
      </view>
    </view>
  </scroll-view>

  <!-- 任务统计 -->
  <view class="task-stats card">
    <view class="stat-item">
      <text class="stat-number">{{stats.total}}</text>
      <text class="stat-label">总任务</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.pending}}</text>
      <text class="stat-label">待开始</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.inProgress}}</text>
      <text class="stat-label">进行中</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.completed}}</text>
      <text class="stat-label">已完成</text>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-list">
    <view
      wx:for="{{taskList}}"
      wx:key="id"
      class="task-item card"
    >
      <!-- 任务头部 -->
      <view class="task-header" bindtap="toggleTaskExpand" data-id="{{item.id}}">
        <view class="task-header-content">
          <view class="task-title-row">
            <text class="task-title">{{item.title}}</text>
            <view class="task-status status-{{item.status}}">
              <text>{{item.statusText}}</text>
            </view>
          </view>
          <view class="task-code-row">
            <text class="task-code">任务编号: {{item.code}}</text>
            <text class="expand-hint">点击查看分组</text>
          </view>
        </view>
        <view class="expand-icon">
          <image
            src="/static/icons/{{expandedTasks[item.id] ? 'arrow-up' : 'arrow-down'}}.png"
            mode="aspectFit"
          ></image>
        </view>
      </view>

      <!-- 任务基本信息 -->
      <view class="task-info">
        <view class="info-row">
          <view class="info-item">
            <text class="info-label">项目:</text>
            <text class="info-text">{{item.projectName || '-'}}</text>
          </view>
        </view>
        <view class="info-row">
          <view class="info-item">
            <text class="info-label">客户:</text>
            <text class="info-text">{{item.customerName || '-'}}</text>
          </view>
        </view>
      </view>

      <!-- 展开的分组列表 -->
      <view wx:if="{{expandedTasks[item.id]}}" class="task-groups">
        <view class="groups-title">任务分组</view>
        <view
          wx:for="{{item.taskGroups}}"
          wx:for-item="group"
          wx:key="id"
          class="group-item"
          bindtap="handleSamplingManagement"
          data-id="{{group.id}}"
        >
          <view class="group-header">
            <view class="group-title">
              <text class="group-code">{{group.groupCode || group.group_code || '-'}}</text>
              <text class="group-cycle">周期{{group.cycleNumber || group.cycle_number || '-'}} {{group.cycleType || group.cycle_type || ''}}</text>
            </view>
            <view class="group-status status-{{group.status}}">
              <text>{{group.statusText || '待执行'}}</text>
            </view>
          </view>
          <view class="group-info">
            <view class="group-detail-row">
              <text class="group-detail">{{group.detectionCategory || group.detection_category || '-'}} | {{group.pointName || group.point_name || '-'}}</text>
              <text class="group-hint">点击管理样品</text>
            </view>
          </view>
        </view>

        <!-- 如果没有分组 -->
        <view wx:if="{{!item.taskGroups || item.taskGroups.length === 0}}" class="no-groups">
          <text>暂无分组信息</text>
        </view>
      </view>

      <!-- 任务进度 -->
      <view class="task-progress">
        <view class="progress-info">
          <text class="progress-text">进度: {{item.completedSamples}}/{{item.totalSamples}}</text>
          <text class="progress-percent">{{item.progress}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{item.progress}}%"></view>
        </view>
      </view>

      <!-- 任务操作 -->
      <view class="task-actions">
        <button
          wx:if="{{item.status === 'pending'}}"
          class="action-btn start-btn"
          bindtap="startTask"
          data-id="{{item.id}}"
          catchtap="stopPropagation"
        >
          开始任务
        </button>
        <button
          wx:if="{{item.status === 'in_progress'}}"
          class="action-btn continue-btn"
          bindtap="continueTask"
          data-id="{{item.id}}"
          catchtap="stopPropagation"
        >
          继续执行
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{taskList.length === 0 && !loading}}" class="empty-state">
      <image src="/static/images/empty-task.png" mode="aspectFit"></image>
      <text class="empty-text">{{searchKeyword ? '未找到相关任务' : '暂无任务'}}</text>
      <button wx:if="{{!searchKeyword}}" class="empty-btn" bindtap="refreshData">刷新数据</button>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && taskList.length > 0}}" class="load-more">
    <view wx:if="{{loadingMore}}" class="loading-more">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
    <button wx:else class="load-more-btn" bindtap="loadMore">加载更多</button>
  </view>
</view>

<!-- 筛选弹窗 -->
<view wx:if="{{showFilterModal}}" class="filter-modal" bindtap="hideFilter">
  <view class="filter-content" catchtap="stopPropagation">
    <view class="filter-header">
      <text class="filter-title">筛选条件</text>
      <image class="close-btn" src="/static/icons/close.png" mode="aspectFit" bindtap="hideFilter"></image>
    </view>
    
    <view class="filter-section">
      <text class="section-title">任务状态</text>
      <view class="filter-options">
        <view 
          wx:for="{{statusOptions}}" 
          wx:key="value"
          class="filter-option {{item.selected ? 'selected' : ''}}"
          bindtap="toggleStatusFilter"
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>

    <view class="filter-section">
      <text class="section-title">计划日期</text>
      <view class="date-range">
        <picker mode="date" value="{{filterStartDate}}" bindchange="onStartDateChange">
          <view class="date-picker">
            <text>{{filterStartDate || '开始日期'}}</text>
            <image src="/static/icons/calendar.png" mode="aspectFit"></image>
          </view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" value="{{filterEndDate}}" bindchange="onEndDateChange">
          <view class="date-picker">
            <text>{{filterEndDate || '结束日期'}}</text>
            <image src="/static/icons/calendar.png" mode="aspectFit"></image>
          </view>
        </picker>
      </view>
    </view>

    <view class="filter-actions">
      <button class="filter-reset" bindtap="resetFilter">重置</button>
      <button class="filter-confirm" bindtap="applyFilter">确定</button>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
