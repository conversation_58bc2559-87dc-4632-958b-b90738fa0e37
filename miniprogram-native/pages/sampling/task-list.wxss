/* pages/sampling/task-list.wxss */

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  background: white;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
}

.clear-search {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search image {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.filter-btn {
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-btn image {
  width: 36rpx;
  height: 36rpx;
}

/* 筛选标签 */
.filter-tags {
  margin-bottom: 20rpx;
}

.tag-list {
  display: flex;
  gap: 16rpx;
  padding: 0 20rpx;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f0f9ff;
  color: #409EFF;
  border-radius: 20rpx;
  font-size: 22rpx;
  white-space: nowrap;
}

.filter-tag.active {
  background: #409EFF;
  color: white;
}

.tag-close {
  width: 20rpx;
  height: 20rpx;
}

/* 任务统计 */
.task-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #909399;
}

/* 任务列表 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.task-item {
  padding: 30rpx;
  transition: transform 0.2s;
}

.task-item:active {
  transform: scale(0.98);
}

/* 任务头部 */
.task-header {
  margin-bottom: 20rpx;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12rpx;
  padding: 16rpx;
  margin: -16rpx -16rpx 20rpx -16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.task-header:hover {
  background-color: rgba(64, 158, 255, 0.05);
}

.task-header:active {
  background-color: rgba(64, 158, 255, 0.1);
  transform: scale(0.98);
}

.task-header-content {
  flex: 1;
}

.task-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.task-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  flex: 1;
  margin-right: 20rpx;
}

.task-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.status-pending {
  background: #f0f9ff;
  color: #409EFF;
}

.status-in_progress {
  background: #f0f9f0;
  color: #67C23A;
}

.status-completed {
  background: #fdf6ec;
  color: #E6A23C;
}

.status-cancelled {
  background: #fef0f0;
  color: #F56C6C;
}

.task-code-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-code {
  font-size: 22rpx;
  color: #909399;
}

.expand-hint {
  font-size: 20rpx;
  color: #409EFF;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.task-header:hover .expand-hint {
  opacity: 1;
  transform: translateX(-4rpx);
}

/* 任务信息 */
.task-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.info-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  opacity: 0.6;
}

.info-text {
  font-size: 24rpx;
  color: #606266;
}

.info-label {
  font-size: 24rpx;
  color: #909399;
  margin-right: 8rpx;
  min-width: 60rpx;
}

/* 展开图标 */
.expand-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 50%;
  transition: all 0.2s ease;
  margin-left: 16rpx;
}

.task-header:hover .expand-icon {
  background-color: rgba(64, 158, 255, 0.2);
  transform: scale(1.1);
}

.expand-icon image {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.task-header:hover .expand-icon image {
  opacity: 1;
}

/* 任务分组 */
.task-groups {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #EBEEF5;
}

.groups-title {
  font-size: 26rpx;
  color: #303133;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.group-item {
  background: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
  border: 1rpx solid #E4E7ED;
  cursor: pointer;
  transition: all 0.2s ease;
}

.group-item:hover {
  background: rgba(64, 158, 255, 0.05);
  border-color: #409EFF;
}

.group-item:active {
  background: rgba(64, 158, 255, 0.1);
  transform: scale(0.98);
}

.group-item:last-child {
  margin-bottom: 0;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.group-title {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.group-code {
  font-size: 26rpx;
  color: #303133;
  font-weight: 600;
  line-height: 1.2;
}

.group-cycle {
  font-size: 22rpx;
  color: #409EFF;
  font-weight: 500;
  line-height: 1.2;
}

.group-name {
  font-size: 26rpx;
  color: #303133;
  font-weight: 500;
}

.group-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.group-info {
  margin-bottom: 0;
}

.group-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-detail {
  font-size: 24rpx;
  color: #606266;
  flex: 1;
}

.group-hint {
  font-size: 20rpx;
  color: #409EFF;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.group-item:hover .group-hint {
  opacity: 1;
  transform: translateX(-4rpx);
}

.group-detail {
  font-size: 24rpx;
  color: #606266;
}

/* 移除分组样品管理按钮相关样式 */

.no-groups {
  text-align: center;
  padding: 40rpx 0;
  color: #909399;
  font-size: 24rpx;
}

/* 任务进度 */
.task-progress {
  margin-bottom: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #606266;
}

.progress-percent {
  font-size: 24rpx;
  color: #409EFF;
  font-weight: bold;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 4rpx;
  transition: width 0.3s;
}

/* 任务操作 */
.task-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  border: none;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.start-btn {
  background: #67C23A;
  color: white;
}

.continue-btn {
  background: #409EFF;
  color: white;
}

.detail-btn {
  background: #f5f5f5;
  color: #606266;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  text-align: center;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #909399;
  margin-bottom: 30rpx;
}

.empty-btn {
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

/* 加载更多 */
.load-more {
  padding: 40rpx 20rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-more-btn {
  background: #f5f5f5;
  color: #606266;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.filter-content {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  color: #606266;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.filter-option.selected {
  background: #409EFF;
  color: white;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.date-picker {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #606266;
}

.date-picker image {
  width: 24rpx;
  height: 24rpx;
}

.date-separator {
  font-size: 24rpx;
  color: #909399;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.filter-reset {
  background: #f5f5f5;
  color: #606266;
}

.filter-confirm {
  background: #409EFF;
  color: white;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-text {
  font-size: 24rpx;
  color: #606266;
  margin-top: 20rpx;
}
