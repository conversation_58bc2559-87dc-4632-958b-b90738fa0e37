// pages/printer/preview.js
const app = getApp()
const CPCLPrintAdapter = require('../../utils/cpcl_print/adapter.js')

Page({
  // 辅助函数：替代扩展运算符
  pushArray: function(target, source) {
    for (let i = 0; i < source.length; i++) {
      target.push(source[i])
    }
  },

  data: {
    // 标签数据
    labelData: null,
    taskInfo: null,
    bottleGroup: null,

    // 预览设置
    previewScale: 1,
    canvasWidth: 300,
    canvasHeight: 310, // 调整高度以匹配60mm标签 (60/58*300≈310)

    // 打印设置
    printerSettings: {
      paperWidth: 58,
      paperHeight: 60, // 60mm高度
      commandSet: 'CPCL'
    },

    // 连接的打印机
    connectedDevice: null
  },

  onLoad(options) {
    console.log('打印预览页面加载', options)

    // 初始化CPCL打印适配器
    this.cpclPrinter = new CPCLPrintAdapter()

    // 获取传递的数据
    if (options.data) {
      try {
        const data = JSON.parse(decodeURIComponent(options.data))
        console.log('解析的数据:', data)

        this.setData({
          taskInfo: data.taskInfo || null,
          bottleGroup: data.bottleGroup || null
        })

        console.log('设置的taskInfo:', data.taskInfo)
        console.log('设置的bottleGroup:', data.bottleGroup)

        // 生成标签数据
        this.generateLabelData()

        // 加载打印机设置
        this.loadPrinterSettings()

      } catch (error) {
        console.error('解析传递数据失败:', error)
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    }
  },

  onReady() {
    console.log('页面准备完成')
    // 绘制将在generateLabelData完成后自动执行
  },

  // 生成标签数据
  generateLabelData() {
    const { taskInfo, bottleGroup } = this.data

    console.log('generateLabelData - taskInfo:', taskInfo)
    console.log('generateLabelData - bottleGroup:', bottleGroup)

    if (!bottleGroup) {
      console.error('缺少瓶组数据，无法生成标签')
      return
    }

    // 如果没有taskInfo，使用默认值或从bottleGroup中获取
    const safeTaskInfo = taskInfo || {}
    
    // 构建样品编号：只包含任务编号，不包含序号
    const taskCode = safeTaskInfo.taskCode || safeTaskInfo.projectName || bottleGroup.taskCode || 'LIMS001'
    const sampleIndex = bottleGroup.sortOrder || 1
    const totalSamples = safeTaskInfo.totalSamples || bottleGroup.totalCount || 1
    const sampleNumber = taskCode // 样品编号不包含序号
    const pageInfo = `${sampleIndex}/${totalSamples}` // 序号信息移到页脚

    // 构建采样点位：点位名称 （周期{周期数}）
    const pointName = safeTaskInfo.pointName || safeTaskInfo.location || bottleGroup.pointName || '采样点位'
    const cycleNumber = safeTaskInfo.cycleNumber || bottleGroup.cycleNumber || 1
    const samplingPoint = `${pointName} （周期${cycleNumber}）`

    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return new Date().toISOString().split('T')[0]
      const date = new Date(dateStr)
      return date.toISOString().split('T')[0]
    }

    // 处理检测项目的两行显示
    const rawTestItems = bottleGroup.detectionMethod || '常规检测'
    const testItemsDisplay = this.processTestItemsForDisplay(rawTestItems)

    // 使用与实际打印相同的数据映射逻辑
    const labelData = {
      sampleCategory: bottleGroup.bottleType || '水样',
      sampleNumber: sampleNumber,
      samplingDate: formatDate(new Date()), // 使用当前日期，与实际打印一致
      samplingPoint: samplingPoint,
      testItems: rawTestItems, // 原始内容
      testItemsDisplay: testItemsDisplay, // 处理后的显示内容
      container: bottleGroup.bottleVolume || '500ml塑料瓶',
      storageMethod: '常温保存',
      pageInfo: pageInfo, // 页码信息（样品序号/总数）
      bottleCode: bottleGroup.bottleGroupCode || `瓶组${bottleGroup.id}`,
      qrCodeText: `${sampleNumber}-${bottleGroup.id}`
    }

    console.log('生成的标签数据:', labelData)
    
    this.setData({ labelData }, () => {
      console.log('标签数据设置完成，准备绘制预览')
      // 数据设置完成后绘制预览
      setTimeout(() => {
        this.drawPreview()
      }, 50)
    })
    console.log('生成的标签数据:', labelData)
  },

  /**
   * 处理检测项目的两行显示
   * @param {string} testItems - 原始检测项目内容
   * @returns {Object} 处理后的显示对象
   */
  processTestItemsForDisplay(testItems) {
    if (!testItems) {
      return {
        isSingleLine: true,
        singleLineText: '常规检测',
        firstLine: '',
        secondLine: ''
      }
    }

    // 计算每行最大字符数（预览页面可以稍微宽松一些）
    const maxCharsPerLine = 22 // 预览页面比打印稍宽
    const labelPrefix = '检测项目：'
    const maxContentChars = maxCharsPerLine - labelPrefix.length

    if (testItems.length <= maxContentChars) {
      // 内容较短，单行显示
      return {
        isSingleLine: true,
        singleLineText: testItems,
        firstLine: '',
        secondLine: ''
      }
    } else {
      // 内容较长，分两行显示
      const splitResult = this.splitTextIntelligently(testItems, maxCharsPerLine)
      return {
        isSingleLine: false,
        singleLineText: '',
        firstLine: splitResult.firstLine,
        secondLine: splitResult.secondLine
      }
    }
  },

  /**
   * 智能分割文本
   * @param {string} text - 要分割的文本
   * @param {number} maxCharsPerLine - 每行最大字符数
   * @returns {Object} 分割结果
   */
  splitTextIntelligently(text, maxCharsPerLine) {
    const firstLineEnd = Math.min(maxCharsPerLine, text.length)
    let splitPoint = firstLineEnd

    // 尝试在合适的位置分割
    if (text.length > maxCharsPerLine) {
      const separators = ['、', '，', ',', ' ', '；', ';']
      let bestSplit = firstLineEnd

      for (let i = Math.min(maxCharsPerLine - 3, text.length - 3); i >= Math.max(maxCharsPerLine - 8, 0); i--) {
        if (separators.includes(text[i])) {
          bestSplit = i + 1
          break
        }
      }
      splitPoint = bestSplit
    }

    const firstLine = text.substring(0, splitPoint).trim()
    const secondLine = text.substring(splitPoint).trim()

    return { firstLine, secondLine }
  },

  // 加载打印机设置
  loadPrinterSettings() {
    try {
      const connectedDevice = wx.getStorageSync('connectedDevice')
      const printerSettings = wx.getStorageSync('printerSettings') || this.data.printerSettings

      this.setData({
        connectedDevice,
        printerSettings
      })

      // 设置CPCL打印适配器的连接设备信息
      if (connectedDevice) {
        this.cpclPrinter.setConnectedDevice(connectedDevice)
        console.log('已设置CPCL打印适配器连接设备:', connectedDevice.deviceId)
      }
    } catch (error) {
      console.error('加载打印机设置失败:', error)
    }
  },

  // 绘制预览 - 使用与实际打印一致的布局
  drawPreview() {
    const { labelData } = this.data
    console.log('开始绘制预览，标签数据:', labelData)

    if (!labelData) {
      console.log('标签数据为空，无法绘制')
      return
    }

    const ctx = wx.createCanvasContext('previewCanvas', this)
    const { canvasWidth, canvasHeight } = this.data

    console.log('Canvas尺寸:', canvasWidth, canvasHeight)

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)

    // 设置背景
    ctx.setFillStyle('#ffffff')
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)

    // 绘制边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(2)
    ctx.strokeRect(5, 5, canvasWidth - 10, canvasHeight - 10)

    // 生成打印内容并绘制
    const printContent = this.generatePrintContent()

    // 绘制所有内容项
    printContent.content.forEach(item => {
      if (item.type === 'text') {
        this.drawTextItem(ctx, item)
      } else if (item.type === 'qrcode') {
        this.drawQRCodeItem(ctx, item)
      }
    })

    // 执行绘制
    ctx.draw(false, () => {
      console.log('Canvas绘制完成')
    })
  },

  // 绘制文本项
  drawTextItem(ctx, item) {
    ctx.setFillStyle('#000000')
    ctx.setFontSize(item.fontSize || 10)

    // 坐标转换：mm转换为像素 (1mm ≈ 3.5px，更紧凑)
    const x = (item.x || 0) * 3.5 + 10 // 添加边距
    const y = (item.y || 0) * 3.5 + 20 // 添加边距

    ctx.fillText(item.text, x, y)
  },

  // 绘制二维码项
  drawQRCodeItem(ctx, item) {
    // 坐标转换：mm转换为像素
    const x = (item.x || 0) * 3.5 + 10
    const y = (item.y || 0) * 3.5 + 20
    const size = (item.size || 16) * 3.5 // 使用更小的尺寸

    // 确保二维码不超出画布边界
    const maxX = this.data.canvasWidth - 15
    const maxY = this.data.canvasHeight - 15
    const adjustedX = Math.min(x, maxX - size)
    const adjustedY = Math.min(y, maxY - size)

    // 绘制二维码边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(1)
    ctx.strokeRect(adjustedX, adjustedY, size, size)

    // 绘制二维码内容（简化版网格）
    ctx.setFillStyle('#000000')
    const cellSize = size / 8 // 减少网格数量
    for (let i = 0; i < 8; i++) {
      for (let j = 0; j < 8; j++) {
        if ((i + j) % 2 === 0) {
          ctx.fillRect(adjustedX + i * cellSize, adjustedY + j * cellSize, cellSize, cellSize)
        }
      }
    }

    // 添加二维码文本说明
    ctx.setFillStyle('#666666')
    ctx.setFontSize(8)
    ctx.fillText('QR', adjustedX + size/2 - 8, adjustedY + size + 12)
  },


  // 重新绘制预览
  redrawPreview() {
    console.log('重新绘制预览')
    // 重新生成标签数据
    this.generateLabelData()
    // 延迟绘制确保数据更新
    setTimeout(() => {
      this.drawPreview()
    }, 100)
  },

  // 打印标签
  async printLabel() {
    const { connectedDevice, labelData } = this.data

    if (!connectedDevice) {
      wx.showModal({
        title: '提示',
        content: '请先配置打印机',
        confirmText: '去配置',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/printer/config'
            })
          }
        }
      })
      return
    }

    if (!labelData) {
      wx.showToast({
        title: '标签数据不完整',
        icon: 'none'
      })
      return
    }

    try {
      // 转换标签数据为CPCL打印适配器需要的格式
      const sampleData = {
        sampleId: labelData.sampleNumber || 'UNKNOWN',
        sampleType: labelData.sampleCategory || '未知类型',
        samplingPoint: labelData.samplingPoint || '未知地点',
        samplingDate: labelData.samplingDate || new Date().toISOString().split('T')[0],
        testItems: labelData.testItems || '常规检测',
        container: labelData.container || '未知容器',
        storageMethod: labelData.storageMethod || '常温保存',
        pageInfo: labelData.pageInfo || '' // 页码信息（如 1/1）
      }

      // 使用CPCL打印适配器进行打印
      const result = await this.cpclPrinter.printSample(sampleData, (progress) => {
        wx.showLoading({
          title: `打印中... ${progress.percentage}%`
        })
      })

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '打印完成',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.message)
      }

    } catch (error) {
      wx.hideLoading()
      console.error('打印失败:', error)
      wx.showModal({
        title: '打印失败',
        content: error.message || '打印过程中出现错误',
        showCancel: false
      })
    }
  },

  // 生成打印内容 - 与实际打印保持一致
  generatePrintContent() {
    const { labelData } = this.data

    // 计算Y坐标 - 与CPCL打印保持一致
    const startY = 2.5; // 20px / 200dpi * 25.4mm ≈ 2.5mm
    const lineHeight = 4.5; // 35px / 200dpi * 25.4mm ≈ 4.5mm
    let yPos = startY;

    const content = [];

    // 1. 样品类别（第一行）
    if (labelData.sampleCategory) {
      content.push({
        type: 'text',
        text: '样品类别：' + labelData.sampleCategory,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 2. 样品编号（第二行）
    if (labelData.sampleNumber) {
      content.push({
        type: 'text',
        text: '样品编号：' + labelData.sampleNumber,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 3. 采样日期（第三行）
    if (labelData.samplingDate) {
      content.push({
        type: 'text',
        text: '采样日期：' + labelData.samplingDate,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 4. 采样点位（第四行）
    if (labelData.samplingPoint) {
      content.push({
        type: 'text',
        text: '采样点位：' + labelData.samplingPoint,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 5. 检测项目（第五行）
    if (labelData.testItems) {
      content.push({
        type: 'text',
        text: '检测项目：' + labelData.testItems,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 6. 保存容器（第六行）
    if (labelData.container) {
      content.push({
        type: 'text',
        text: '保存容器：' + labelData.container,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 7. 保存方式（第七行）
    if (labelData.storageMethod) {
      content.push({
        type: 'text',
        text: '保存方式：' + labelData.storageMethod,
        x: 1.3,
        y: yPos,
        fontSize: 8
      });
      yPos += lineHeight;
    }

    // 8. 样品状态（第八行）- 固定格式，使用正方形方框
    content.push({
      type: 'text',
      text: '样品状态：□待测  □在测  □测毕  □留样',
      x: 1.3,
      y: yPos,
      fontSize: 8
    });
    yPos += lineHeight;

    // 二维码 - 右上角位置
    content.push({
      type: 'qrcode',
      text: labelData.qrCodeText || labelData.sampleNumber,
      x: 51, // 400px / 200dpi * 25.4mm ≈ 51mm
      y: 2, // 16px / 200dpi * 25.4mm ≈ 2mm
      size: 20 // 调整尺寸
    });

    // 页脚 - 浙江求实环境监测有限公司 + 页码
    // 调整Y坐标与实际打印一致
    const footerYmm = 50.8; // 400px / 200dpi * 25.4mm ≈ 50.8mm
    const pageInfo = labelData.pageInfo || ''; // 例如 "3/4"
    if (pageInfo) {
      // 左侧公司名称
      content.push({
        type: 'text',
        text: '浙江求实环境监测有限公司',
        x: 1.3,
        y: footerYmm,
        fontSize: 8
      });
      // 右侧页码
      content.push({
        type: 'text',
        text: pageInfo,
        x: 53, // 420px / 200dpi * 25.4mm ≈ 53mm
        y: footerYmm,
        fontSize: 8
      });
    } else {
      content.push({
        type: 'text',
        text: '浙江求实环境监测有限公司',
        x: 1.3,
        y: footerYmm,
        fontSize: 8
      });
    }

    return {
      // 标签尺寸 (mm) - 60mm高度
      width: 58,
      height: 60,
      content: content
    }
  },

  // 生成CPCL打印指令 - 使用增强的打印器
  generateCPCLCommands(content, settings) {
    // 使用增强打印器生成指令，解决中文乱码问题
    return this.enhancedPrinter.generateEnhancedCPCLCommands(content, settings)
  },

  // 字符串转字节数组
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i))
    }
    return bytes
  },

  // 发送BLE数据 - 使用增强的发送方法
  async sendBLEData(device, data) {
    // 使用增强打印器的发送方法，支持重试和错误处理
    return this.enhancedPrinter.sendEnhancedBLEData(device, data, (progress) => {
      console.log(`打印进度: ${progress.percentage}%`)
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})
