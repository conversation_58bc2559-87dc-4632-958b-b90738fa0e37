<!-- pages/printer/preview.wxml -->
<view class="preview-container">
  <!-- 顶部导航 -->
  <view class="preview-header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
    <view class="header-title">标签预览</view>
    <view class="header-right"></view>
  </view>

  <!-- 预览区域 -->
  <view class="preview-content">
    <view class="preview-title">打印预览</view>
    <view class="preview-subtitle">75mm × 60mm 标签纸</view>
    
    <!-- Canvas预览 -->
    <view class="canvas-container">
      <canvas
        canvas-id="previewCanvas"
        class="preview-canvas"
        style="width: 300px; height: 240px;"
      ></canvas>
    </view>
    
    <!-- 标签信息详情 -->
    <view class="label-details" wx:if="{{labelData}}">
      <view class="details-title">标签内容</view>
      
      <view class="detail-row">
        <text class="detail-label">样品类别：</text>
        <text class="detail-value">{{labelData.sampleCategory}}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">样品编号：</text>
        <text class="detail-value">{{labelData.sampleNumber}}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">采样日期：</text>
        <text class="detail-value">{{labelData.samplingDate}}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">采样点位：</text>
        <text class="detail-value">{{labelData.samplingPoint}}</text>
      </view>
      
      <!-- 检测项目（支持两行显示） -->
      <view class="detail-row" wx:if="{{labelData.testItemsDisplay.isSingleLine}}">
        <text class="detail-label">检测项目：</text>
        <text class="detail-value">{{labelData.testItemsDisplay.singleLineText}}</text>
      </view>

      <view class="detail-row-multiline" wx:else>
        <view class="detail-row">
          <text class="detail-label">检测项目：</text>
        </view>
        <view class="detail-row detail-content-indent">
          <text class="detail-value">{{labelData.testItemsDisplay.firstLine}}</text>
        </view>
        <view class="detail-row detail-content-indent" wx:if="{{labelData.testItemsDisplay.secondLine}}">
          <text class="detail-value">{{labelData.testItemsDisplay.secondLine}}</text>
        </view>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">保存容器：</text>
        <text class="detail-value">{{labelData.container}}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">保存方式：</text>
        <text class="detail-value">{{labelData.storageMethod}}</text>
      </view>
      
      <view class="detail-row">
        <text class="detail-label">检测单位：</text>
        <text class="detail-value">{{labelData.companyName}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="preview-footer">
    <view class="footer-buttons">
      <button class="btn btn-secondary" bindtap="redrawPreview">
        重新预览
      </button>
      <button class="btn btn-primary" bindtap="printLabel">
        确认打印
      </button>
    </view>
    
    <!-- 打印机状态 -->
    <view class="printer-status" wx:if="{{connectedDevice}}">
      <text class="status-icon">🖨️</text>
      <text class="status-text">已连接：{{connectedDevice.name || connectedDevice.displayName}}</text>
    </view>
    <view class="printer-status no-printer" wx:else>
      <text class="status-icon">⚠️</text>
      <text class="status-text">未连接打印机</text>
    </view>
  </view>
</view>
