/**
 * CPCL蓝牙打印适配器
 * 将cpcl-bluetooth-print项目的功能适配到LIMS小程序中
 */

const { CPCL } = require('./bluetooth-v2/cpcl.js');

class CPCLPrintAdapter {
  constructor() {
    this.cpcl = new CPCL({
      mode: 'CPCL',
      maxWidth: 540,  // 58mm纸张宽度
      size: 24        // 字体大小
    });
    
    // 蓝牙连接相关
    this.connectedDevice = null;
    this.isConnecting = false;
    this.printList = [];
  }

  /**
   * 初始化蓝牙适配器
   */
  async initBluetooth() {
    return new Promise((resolve, reject) => {
      if (!wx.openBluetoothAdapter) {
        reject(new Error('当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'));
        return;
      }

      console.log('开始初始化蓝牙适配器...');

      wx.openBluetoothAdapter({
        success: (res) => {
          console.log('蓝牙初始化成功', res);

          // 检查蓝牙状态
          wx.getBluetoothAdapterState({
            success: (state) => {
              console.log('蓝牙适配器状态:', state);
              if (!state.available) {
                reject(new Error('蓝牙不可用，请检查设备蓝牙功能'));
                return;
              }
              resolve(res);
            },
            fail: (err) => {
              console.error('获取蓝牙状态失败:', err);
              resolve(res); // 即使获取状态失败也继续
            }
          });
        },
        fail: (err) => {
          console.error('蓝牙初始化失败', err);

          let errorMsg = '蓝牙初始化失败';
          if (err.errCode === 10001) {
            errorMsg = '未找到蓝牙适配器，请检查设备是否支持蓝牙';
          } else if (err.errCode === 10000) {
            errorMsg = '未授权使用蓝牙，请在设置中开启权限';
          } else if (err.errMsg) {
            errorMsg = `蓝牙初始化失败：${err.errMsg}`;
          } else {
            errorMsg = '蓝牙初始化失败，请确保已开启手机蓝牙，且已开启小程序使用权限';
          }

          reject(new Error(errorMsg));
        }
      });
    });
  }

  /**
   * 搜索蓝牙打印机设备
   */
  async searchDevices() {
    return new Promise((resolve, reject) => {
      // 检查缓存
      const cachedList = wx.getStorageSync('bluetoothPrintList');
      if (cachedList && cachedList.length > 0) {
        this.printList = cachedList;
        resolve(cachedList);
        return;
      }

      wx.showLoading({
        title: '搜索中',
        mask: true
      });

      wx.startBluetoothDevicesDiscovery({
        services: [],
        complete: (res) => {
          setTimeout(() => {
            wx.getBluetoothDevices({
              complete: (res) => {
                wx.hideLoading();
                const list = this.filterPrintDevices(res.devices);
                this.printList = list;
                wx.setStorageSync('bluetoothPrintList', list);
                
                if (list.length === 0) {
                  reject(new Error('没有发现新的设备'));
                } else {
                  resolve(list);
                }
              }
            });
            
            wx.stopBluetoothDevicesDiscovery({
              success: () => console.log('停止搜索设备')
            });
          }, 3000);
        }
      });
    });
  }

  /**
   * 过滤打印机设备
   */
  filterPrintDevices(devices) {
    const printList = [];
    
    for (let i = 0; i < devices.length; i++) {
      const device = devices[i];
      if (!device.advertisData) continue;
      
      const str = Array.prototype.map.call(
        new Uint8Array(device.advertisData), 
        x => ('00' + x.toString(16)).slice(-2)
      ).join('');
      
      if (str.length === 16) {
        device.address = str.toUpperCase();
        printList.push(device);
      }
    }
    
    return printList;
  }

  /**
   * 连接蓝牙设备
   */
  async connectDevice(deviceId) {
    return new Promise((resolve, reject) => {
      if (this.isConnecting) {
        reject(new Error('正在连接中，请稍候'));
        return;
      }

      this.isConnecting = true;

      console.log('开始连接蓝牙设备:', deviceId);

      wx.showLoading({
        title: '连接中...',
        mask: true
      });

      wx.createBLEConnection({
        deviceId,
        timeout: 10000, // 10秒超时
        success: (res) => {
          console.log('BLE连接成功', res);
          this.connectedDevice = { deviceId };
          this.getDeviceServices(deviceId)
            .then(resolve)
            .catch((error) => {
              console.error('获取服务失败:', error);
              // 连接失败时断开连接
              wx.closeBLEConnection({ deviceId });
              reject(error);
            });
        },
        fail: (res) => {
          wx.hideLoading();
          this.isConnecting = false;
          console.error('BLE连接失败', res);

          // 根据错误码提供更详细的错误信息
          let errorMsg = '连接失败';
          if (res.errCode === 10003) {
            errorMsg = '连接失败：设备未找到或已关闭';
          } else if (res.errCode === 10012) {
            errorMsg = '连接失败：连接超时';
          } else if (res.errCode === -1) {
            errorMsg = '连接失败：系统错误，请重启蓝牙';
          } else if (res.errMsg) {
            errorMsg = `连接失败：${res.errMsg}`;
          }

          reject(new Error(errorMsg));
        }
      });
    });
  }

  /**
   * 获取设备服务
   */
  async getDeviceServices(deviceId) {
    return new Promise((resolve, reject) => {
      console.log('开始获取设备服务:', deviceId);

      wx.getBLEDeviceServices({
        deviceId,
        success: (res) => {
          console.log('获取到的服务列表:', res.services);

          const serviceId = this.filterService(res.services);
          if (serviceId) {
            console.log('找到打印机服务:', serviceId);
            this.connectedDevice.serviceId = serviceId;
            this.getDeviceCharacteristics(deviceId, serviceId)
              .then(resolve)
              .catch(reject);
          } else {
            wx.hideLoading();
            this.isConnecting = false;
            console.error('没有找到打印机服务，可用服务:', res.services.map(s => s.uuid));
            reject(new Error('没有找到打印机服务，请确认设备类型'));
          }
        },
        fail: (res) => {
          wx.hideLoading();
          this.isConnecting = false;
          console.error('获取设备服务失败:', res);
          reject(new Error(`获取设备服务失败：${res.errMsg || '未知错误'}`));
        }
      });
    });
  }

  /**
   * 过滤服务UUID
   */
  filterService(services) {
    const serviceUUIDs = [
      "0000EEE0-0000-1000-8000-00805F9B34FB",
      "0000FF00-0000-1000-8000-00805F9B34FB", 
      "49535343-FE7D-4AE5-8FA9-9FAFD205E455"
    ];

    for (let i = 0; i < services.length; i++) {
      const serverid = services[i].uuid.toUpperCase();
      for (let uuid of serviceUUIDs) {
        if (serverid.indexOf(uuid) !== -1) {
          return services[i].uuid;
        }
      }
    }
    return null;
  }

  /**
   * 获取设备特征值
   */
  async getDeviceCharacteristics(deviceId, serviceId) {
    return new Promise((resolve, reject) => {
      console.log('开始获取设备特征值:', { deviceId, serviceId });

      wx.getBLEDeviceCharacteristics({
        deviceId,
        serviceId,
        success: (res) => {
          console.log('获取到的特征值列表:', res.characteristics);

          const writeId = this.filterCharacteristic(res.characteristics);
          if (writeId) {
            console.log('找到可写特征值:', writeId);
            this.connectedDevice.characteristicId = writeId;
            wx.hideLoading();
            this.isConnecting = false;
            console.log('连接完成，设备信息:', this.connectedDevice);
            resolve(this.connectedDevice);
          } else {
            wx.hideLoading();
            this.isConnecting = false;
            console.error('没有找到可写特征值，可用特征值:', res.characteristics);
            reject(new Error('没有找到可写特征值，设备不支持'));
          }
        },
        fail: (res) => {
          wx.hideLoading();
          this.isConnecting = false;
          console.error('获取特征值失败:', res);
          reject(new Error(`获取特征值失败：${res.errMsg || '未知错误'}`));
        }
      });
    });
  }

  /**
   * 过滤可写特征值
   */
  filterCharacteristic(characteristics) {
    for (let i = 0; i < characteristics.length; i++) {
      const charc = characteristics[i];
      if (charc.properties.write) {
        return charc.uuid;
      }
    }
    return null;
  }

  /**
   * 生成CPCL打印命令 - 根据实际打印效果优化
   */
  generateCPCLCommands(sampleData) {
    // 初始化CPCL
    this.cpcl.init();

    // 设置标签尺寸 (58mm宽度，60mm高度) - 修正走纸距离
    // ! 偏移量 DPI DPI 高度 数量
    this.cpcl.addCommand('! 0 200 200 472 1'); // 60mm = 472像素 (200dpi)

    let yPos = 20;
    const lineHeight = 35; // 行间距35像素

    // 1. 样品类别（第一行）
    if (sampleData.sampleType) {
      this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 样品类别：${sampleData.sampleType}`);
      yPos += lineHeight;
    }

    // 2. 样品编号（第二行）
    if (sampleData.sampleId) {
      this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 样品编号：${sampleData.sampleId}`);
      yPos += lineHeight;
    }

    // 3. 采样日期（第三行）
    if (sampleData.samplingDate || sampleData.date) {
      const date = sampleData.samplingDate || sampleData.date;
      this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 采样日期：${date}`);
      yPos += lineHeight;
    }

    // 4. 采样点位（第四行）
    if (sampleData.samplingPoint || sampleData.location) {
      const point = sampleData.samplingPoint || sampleData.location;
      this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 采样点位：${point}`);
      yPos += lineHeight;
    }

    // 5. 检测项目（第五行，支持两行显示）
    if (sampleData.testItems || sampleData.items) {
      const items = sampleData.testItems || (Array.isArray(sampleData.items) ? sampleData.items.join('、') : sampleData.items);

      // 计算每行最大字符数（考虑标签宽度和字体大小）
      const maxCharsPerLine = 20; // 58mm纸张，字体3号，大约20个字符
      const labelPrefix = '检测项目：';
      const maxContentChars = maxCharsPerLine - labelPrefix.length;

      if (items.length <= maxContentChars) {
        // 内容较短，单行显示
        this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} ${labelPrefix}${items}`);
        yPos += lineHeight;
      } else {
        // 内容较长，分两行显示
        this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} ${labelPrefix}`);
        yPos += lineHeight;

        // 将长文本分割为两行
        const firstLineEnd = Math.min(maxCharsPerLine, items.length);
        let splitPoint = firstLineEnd;

        // 尝试在合适的位置分割（避免在字符中间分割）
        if (items.length > maxCharsPerLine) {
          // 寻找最近的分隔符（逗号、顿号、空格等）
          const separators = ['、', '，', ',', ' ', '；', ';'];
          let bestSplit = firstLineEnd;

          for (let i = Math.min(maxCharsPerLine - 3, items.length - 3); i >= Math.max(maxCharsPerLine - 8, 0); i--) {
            if (separators.includes(items[i])) {
              bestSplit = i + 1;
              break;
            }
          }
          splitPoint = bestSplit;
        }

        const firstLine = items.substring(0, splitPoint).trim();
        const secondLine = items.substring(splitPoint).trim();

        // 打印第一行内容（缩进对齐）
        this.cpcl.addCommand(`TEXT 3 0 90 ${yPos} ${firstLine}`);
        yPos += lineHeight;

        // 打印第二行内容（如果有）
        if (secondLine) {
          this.cpcl.addCommand(`TEXT 3 0 90 ${yPos} ${secondLine}`);
          yPos += lineHeight;
        }
      }
    }

    // 6. 保存容器（第六行）
    if (sampleData.container) {
      this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 保存容器：${sampleData.container}`);
      yPos += lineHeight;
    }

    // 7. 保存方式（第七行）
    if (sampleData.storageMethod || sampleData.storage) {
      const storage = sampleData.storageMethod || sampleData.storage;
      this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 保存方式：${storage}`);
      yPos += lineHeight;
    }

    // 8. 样品状态（第八行）- 固定格式，使用正方形方框
    // 使用 □ (U+25A1) 代替 ☐，这是标准的正方形方框字符
    this.cpcl.addCommand(`TEXT 3 0 10 ${yPos} 样品状态：□待测  □在测  □测毕  □留样`);
    yPos += lineHeight;

    // 二维码 - 右上角位置
    if (sampleData.sampleId) {
      this.cpcl.addCommand(`BARCODE QR 400 16 M 2 U 4`);
      this.cpcl.addCommand(`MA,${sampleData.sampleId}`);
      this.cpcl.addCommand('ENDQR');
    }

    // 页脚 - 浙江求实环境监测有限公司 + 页码
    // 调整Y坐标，确保在标签范围内（472像素高度）
    // 根据实际打印测试，调整到400px以避免被裁剪
    const footerY = 400; // 底部边距：472 - 400 - 20 = 52px (6.6mm)
    const pageInfo = sampleData.pageInfo || ''; // 例如 "3/4"
    if (pageInfo) {
      // 左侧公司名称
      this.cpcl.addCommand(`TEXT 3 0 10 ${footerY} 浙江求实环境监测有限公司`);
      // 右侧页码
      this.cpcl.addCommand(`TEXT 3 0 420 ${footerY} ${pageInfo}`);
    } else {
      this.cpcl.addCommand(`TEXT 3 0 10 ${footerY} 浙江求实环境监测有限公司`);
    }

    // 打印命令
    this.cpcl.setPagePrint();

    return this.cpcl.getData();
  }

  /**
   * 发送数据到蓝牙打印机
   */
  async sendDataToPrinter(data, onProgress = null) {
    if (!this.connectedDevice) {
      throw new Error('请先连接打印机');
    }

    return new Promise((resolve, reject) => {
      // 数据分包处理
      const chunks = this.splitDataToChunks(data, 20);
      let currentIndex = 0;

      const sendNextChunk = () => {
        if (currentIndex >= chunks.length) {
          wx.hideLoading();
          resolve('打印完成');
          return;
        }

        const chunk = chunks[currentIndex];
        const progress = Math.round((currentIndex / chunks.length) * 100);
        
        if (onProgress) {
          onProgress({ 
            current: currentIndex + 1, 
            total: chunks.length, 
            percentage: progress 
          });
        }

        wx.writeBLECharacteristicValue({
          deviceId: this.connectedDevice.deviceId,
          serviceId: this.connectedDevice.serviceId,
          characteristicId: this.connectedDevice.characteristicId,
          value: chunk,
          success: () => {
            currentIndex++;
            setTimeout(sendNextChunk, 50); // 50ms延时
          },
          fail: (res) => {
            wx.hideLoading();
            console.error('数据发送失败', res);
            reject(new Error('打印失败'));
          }
        });
      };

      wx.showLoading({
        title: '正在打印...',
        mask: true
      });

      sendNextChunk();
    });
  }

  /**
   * 数据分包
   */
  splitDataToChunks(data, chunkSize) {
    const chunks = [];
    const uint8Array = Array.from(data);
    
    for (let i = 0; i < uint8Array.length; i += chunkSize) {
      const chunk = uint8Array.slice(i, i + chunkSize);
      const buffer = new ArrayBuffer(chunk.length);
      const dataView = new DataView(buffer);
      
      for (let j = 0; j < chunk.length; j++) {
        dataView.setUint8(j, chunk[j]);
      }
      
      chunks.push(buffer);
    }
    
    return chunks;
  }

  /**
   * 断开蓝牙连接
   */
  async disconnect() {
    if (this.connectedDevice) {
      wx.closeBLEConnection({
        deviceId: this.connectedDevice.deviceId,
        success: () => {
          console.log('蓝牙连接已断开');
          this.connectedDevice = null;
        }
      });
    }
  }

  /**
   * 设置已连接的设备信息
   */
  setConnectedDevice(deviceInfo) {
    if (deviceInfo && deviceInfo.deviceId) {
      this.connectedDevice = {
        deviceId: deviceInfo.deviceId,
        serviceId: deviceInfo.serviceId,
        characteristicId: deviceInfo.characteristicId
      };
      console.log('设置连接设备:', this.connectedDevice);
    }
  }

  /**
   * 从存储中恢复连接状态
   */
  restoreConnectionFromStorage() {
    try {
      const savedDevice = wx.getStorageSync('connectedDevice');
      if (savedDevice && savedDevice.deviceId) {
        this.setConnectedDevice(savedDevice);
        return true;
      }
    } catch (error) {
      console.error('恢复连接状态失败:', error);
    }
    return false;
  }

  /**
   * 完整的打印流程
   */
  async printSample(sampleData, onProgress = null) {
    try {
      // 如果没有连接设备，尝试从存储中恢复
      if (!this.connectedDevice) {
        const restored = this.restoreConnectionFromStorage();
        if (!restored) {
          throw new Error('请先连接打印机');
        }
      }

      // 生成CPCL命令
      const commands = this.generateCPCLCommands(sampleData);

      // 发送到打印机
      await this.sendDataToPrinter(commands, onProgress);

      return { success: true, message: '打印完成' };
    } catch (error) {
      console.error('打印失败', error);
      return { success: false, message: error.message };
    }
  }
}

module.exports = CPCLPrintAdapter;
