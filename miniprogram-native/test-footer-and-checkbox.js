/**
 * 测试页脚打印和勾选框显示
 * 1. 验证页脚是否在标签范围内
 * 2. 验证勾选框是否为正方形
 */

const CPCLPrintAdapter = require('./utils/cpcl_print/adapter.js');

console.log('=== 页脚和勾选框修复测试 ===\n');

// 测试1: 验证页脚Y坐标
console.log('1. 验证页脚Y坐标...');
try {
  const cpclPrinter = new CPCLPrintAdapter();
  
  const testData = {
    sampleType: '无组织废气',
    sampleId: '24120001Q475',
    samplingDate: '2024-12-31',
    samplingPoint: '厂界下风向3（周期1）',
    testItems: '臭气浓度',
    container: '10L气袋 10L',
    storageMethod: '17-25℃常温保存 密封 避光 无',
    pageInfo: '3/4'
  };

  const commands = cpclPrinter.generateCPCLCommands(testData);
  const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
  
  // 提取标签高度
  const sizeMatch = commandStr.match(/! 0 200 200 (\d+) 1/);
  const labelHeight = sizeMatch ? parseInt(sizeMatch[1]) : 0;
  
  // 提取页脚Y坐标
  const footerMatches = commandStr.matchAll(/TEXT \d+ \d+ \d+ (\d+) [^\r\n]*(?:浙江求实|\/)/g);
  const footerYs = Array.from(footerMatches).map(match => parseInt(match[1]));
  
  console.log('标签高度:', labelHeight, 'px');
  console.log('页脚Y坐标:', footerYs);
  
  if (footerYs.length > 0) {
    const maxFooterY = Math.max(...footerYs);
    const textHeight = 20; // 估计文本高度
    const totalHeight = maxFooterY + textHeight;
    
    console.log('- 最大Y坐标:', maxFooterY, 'px');
    console.log('- 文本高度:', textHeight, 'px');
    console.log('- 总高度:', totalHeight, 'px');
    console.log('- 标签高度:', labelHeight, 'px');
    
    if (totalHeight <= labelHeight) {
      console.log('✅ 页脚在标签范围内');
      const margin = labelHeight - totalHeight;
      console.log('- 底部边距:', margin, 'px');
    } else {
      console.log('❌ 页脚超出标签范围');
      const overflow = totalHeight - labelHeight;
      console.log('- 超出:', overflow, 'px');
    }
  } else {
    console.log('❌ 未找到页脚');
  }
  
} catch (error) {
  console.log('❌ 页脚Y坐标测试失败:', error.message);
}

// 测试2: 验证勾选框字符
console.log('\n2. 验证勾选框字符...');
try {
  const cpclPrinter = new CPCLPrintAdapter();
  
  const testData = {
    sampleType: '测试样品',
    sampleId: 'TEST-001',
    samplingDate: '2024-01-15',
    samplingPoint: '测试点位',
    testItems: '测试项目',
    container: '测试容器',
    storageMethod: '测试保存'
  };

  const commands = cpclPrinter.generateCPCLCommands(testData);
  const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
  
  // 查找样品状态行
  const statusMatch = commandStr.match(/样品状态：([^\r\n]+)/);
  if (statusMatch) {
    const statusText = statusMatch[1];
    console.log('样品状态内容:', statusText);
    
    // 检查使用的方框字符
    const hasSquare = statusText.includes('□'); // U+25A1 正方形
    const hasOldBox = statusText.includes('☐'); // U+2610 投票框
    
    console.log('- 使用正方形方框 (□):', hasSquare ? '✅' : '❌');
    console.log('- 使用旧方框 (☐):', hasOldBox ? '是 ❌' : '否 ✅');
    
    if (hasSquare && !hasOldBox) {
      console.log('✅ 勾选框字符正确');
    } else if (hasOldBox) {
      console.log('❌ 仍在使用旧的方框字符');
    } else {
      console.log('⚠️  未找到方框字符');
    }
    
    // 检查字符编码
    const chars = Array.from(statusText);
    const boxChars = chars.filter(c => c === '□' || c === '☐');
    console.log('- 方框字符数量:', boxChars.length);
    console.log('- 预期数量: 4');
    
    if (boxChars.length === 4) {
      console.log('✅ 方框数量正确');
    } else {
      console.log('❌ 方框数量不正确');
    }
  } else {
    console.log('❌ 未找到样品状态字段');
  }
  
} catch (error) {
  console.log('❌ 勾选框字符测试失败:', error.message);
}

// 测试3: 验证完整布局
console.log('\n3. 验证完整布局...');
try {
  const cpclPrinter = new CPCLPrintAdapter();
  
  const testData = {
    sampleType: '无组织废气',
    sampleId: '24120001Q475',
    samplingDate: '2024-12-31',
    samplingPoint: '厂界下风向3（周期1）',
    testItems: '臭气浓度',
    container: '10L气袋 10L',
    storageMethod: '17-25℃常温保存 密封 避光 无',
    pageInfo: '3/4'
  };

  const commands = cpclPrinter.generateCPCLCommands(testData);
  const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
  
  // 提取所有TEXT命令的Y坐标
  const textMatches = commandStr.matchAll(/TEXT \d+ \d+ \d+ (\d+) ([^\r\n]+)/g);
  const textItems = Array.from(textMatches).map(match => ({
    y: parseInt(match[1]),
    text: match[2].substring(0, 30)
  }));
  
  console.log('完整布局（Y坐标）:');
  textItems.forEach((item, index) => {
    console.log(`${index + 1}. Y=${item.y}px - ${item.text}`);
  });
  
  // 验证关键点
  console.log('\n关键点验证:');
  
  // 1. 样品状态包含正方形方框
  const statusItem = textItems.find(item => item.text.includes('样品状态'));
  if (statusItem) {
    const hasSquare = statusItem.text.includes('□');
    console.log('- 样品状态使用正方形:', hasSquare ? '✅' : '❌');
  }
  
  // 2. 页脚存在
  const footerItems = textItems.filter(item => 
    item.text.includes('浙江求实') || item.text.includes('/')
  );
  console.log('- 页脚项数量:', footerItems.length, footerItems.length >= 1 ? '✅' : '❌');
  
  // 3. 页脚Y坐标
  if (footerItems.length > 0) {
    const footerY = footerItems[0].y;
    console.log('- 页脚Y坐标:', footerY, 'px');
    
    if (footerY === 450) {
      console.log('- 页脚位置:', '正确 ✅');
    } else {
      console.log('- 页脚位置:', `不正确（应为450px）❌`);
    }
  }
  
} catch (error) {
  console.log('❌ 完整布局验证失败:', error.message);
}

// 测试4: 字符Unicode验证
console.log('\n4. 字符Unicode验证...');
try {
  const square = '□'; // U+25A1
  const oldBox = '☐'; // U+2610
  
  console.log('字符对比:');
  console.log('- 正方形方框 (□):');
  console.log('  Unicode: U+' + square.charCodeAt(0).toString(16).toUpperCase());
  console.log('  名称: WHITE SQUARE');
  console.log('  显示: ' + square);
  
  console.log('- 旧方框 (☐):');
  console.log('  Unicode: U+' + oldBox.charCodeAt(0).toString(16).toUpperCase());
  console.log('  名称: BALLOT BOX');
  console.log('  显示: ' + oldBox);
  
  console.log('\n推荐使用: □ (U+25A1) 正方形方框');
  console.log('原因: 更标准的正方形，打印效果更好');
  
} catch (error) {
  console.log('❌ Unicode验证失败:', error.message);
}

// 测试5: 页脚位置计算
console.log('\n5. 页脚位置计算验证...');
try {
  const labelHeight = 472; // 60mm
  const footerY = 450;
  const textHeight = 20; // 估计
  
  console.log('页脚位置计算:');
  console.log('- 标签高度:', labelHeight, 'px (60mm)');
  console.log('- 页脚Y坐标:', footerY, 'px');
  console.log('- 文本高度:', textHeight, 'px (估计)');
  console.log('- 页脚底部:', footerY + textHeight, 'px');
  console.log('- 底部边距:', labelHeight - (footerY + textHeight), 'px');
  
  const footerMM = (footerY / 200 * 25.4).toFixed(1);
  const marginMM = ((labelHeight - footerY - textHeight) / 200 * 25.4).toFixed(1);
  
  console.log('\n毫米单位:');
  console.log('- 页脚位置:', footerMM, 'mm');
  console.log('- 底部边距:', marginMM, 'mm');
  
  if (footerY + textHeight <= labelHeight) {
    console.log('\n✅ 页脚位置合理，在标签范围内');
  } else {
    console.log('\n❌ 页脚可能超出标签范围');
  }
  
} catch (error) {
  console.log('❌ 位置计算失败:', error.message);
}

console.log('\n=== 测试总结 ===');
console.log('✅ 修复1: 页脚Y坐标调整到450px');
console.log('  - 原坐标: 440px');
console.log('  - 新坐标: 450px');
console.log('  - 确保在472px标签范围内');

console.log('\n✅ 修复2: 勾选框字符更换');
console.log('  - 原字符: ☐ (U+2610 BALLOT BOX)');
console.log('  - 新字符: □ (U+25A1 WHITE SQUARE)');
console.log('  - 效果: 更标准的正方形');

console.log('\n📋 最终效果:');
console.log('样品状态：□待测  □在测  □测毕  □留样');
console.log('...');
console.log('浙江求实环境监测有限公司                    3/4');

console.log('\n🎉 页脚和勾选框修复完成！');
