/**
 * 测试检测项目两行显示功能
 */

// 模拟CPCL适配器的检测项目处理逻辑
function testDetectionMethodTwoLines() {
  console.log('=== 检测项目两行显示测试 ===\n')

  // 测试用例
  const testCases = [
    {
      name: '短文本测试',
      testItems: 'pH值、溶解氧',
      expected: '单行显示'
    },
    {
      name: '中等长度文本测试', 
      testItems: 'pH值、溶解氧、化学需氧量、生化需氧量',
      expected: '单行显示（边界情况）'
    },
    {
      name: '长文本测试（有分隔符）',
      testItems: 'pH值、溶解氧、化学需氧量、生化需氧量、氨氮、总磷、总氮、悬浮物',
      expected: '两行显示，在分隔符处分割'
    },
    {
      name: '超长文本测试（无分隔符）',
      testItems: '重金属铅镉汞砷铬镍铜锌检测分析方法原子吸收分光光度法',
      expected: '两行显示，强制分割'
    },
    {
      name: '包含多种分隔符',
      testItems: 'pH值，溶解氧；化学需氧量、生化需氧量 氨氮、总磷',
      expected: '两行显示，优先选择合适分隔符'
    }
  ]

  testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: ${testCase.name}`)
    console.log(`输入: "${testCase.testItems}"`)
    console.log(`预期: ${testCase.expected}`)
    
    const result = processDetectionMethod(testCase.testItems)
    console.log(`结果:`)
    result.commands.forEach((cmd, i) => {
      console.log(`  第${i + 1}行: ${cmd}`)
    })
    console.log(`行数: ${result.lineCount}`)
    console.log('---\n')
  })
}

/**
 * 模拟检测项目处理逻辑
 */
function processDetectionMethod(items) {
  const commands = []
  let yPos = 100 // 模拟Y坐标
  const lineHeight = 35
  
  // 计算每行最大字符数
  const maxCharsPerLine = 20
  const labelPrefix = '检测项目：'
  const maxContentChars = maxCharsPerLine - labelPrefix.length
  
  if (items.length <= maxContentChars) {
    // 内容较短，单行显示
    commands.push(`TEXT 3 0 10 ${yPos} ${labelPrefix}${items}`)
    return { commands, lineCount: 1 }
  } else {
    // 内容较长，分两行显示
    commands.push(`TEXT 3 0 10 ${yPos} ${labelPrefix}`)
    yPos += lineHeight
    
    // 将长文本分割为两行
    const firstLineEnd = Math.min(maxCharsPerLine, items.length)
    let splitPoint = firstLineEnd
    
    // 尝试在合适的位置分割
    if (items.length > maxCharsPerLine) {
      const separators = ['、', '，', ',', ' ', '；', ';']
      let bestSplit = firstLineEnd
      
      for (let i = Math.min(maxCharsPerLine - 3, items.length - 3); i >= Math.max(maxCharsPerLine - 8, 0); i--) {
        if (separators.includes(items[i])) {
          bestSplit = i + 1
          break
        }
      }
      splitPoint = bestSplit
    }
    
    const firstLine = items.substring(0, splitPoint).trim()
    const secondLine = items.substring(splitPoint).trim()
    
    // 打印第一行内容（缩进对齐）
    commands.push(`TEXT 3 0 90 ${yPos} ${firstLine}`)
    yPos += lineHeight
    
    // 打印第二行内容（如果有）
    let lineCount = 2
    if (secondLine) {
      commands.push(`TEXT 3 0 90 ${yPos} ${secondLine}`)
      lineCount = 3
    }
    
    return { commands, lineCount }
  }
}

/**
 * 测试字符宽度计算
 */
function testCharacterWidth() {
  console.log('\n=== 字符宽度测试 ===\n')
  
  const testStrings = [
    'pH值',
    '检测项目：',
    'pH值、溶解氧、化学需氧量',
    '重金属铅镉汞砷铬镍铜锌',
    'ABC123',
    '中英文Mixed测试'
  ]
  
  testStrings.forEach(str => {
    console.log(`"${str}" - 长度: ${str.length} 字符`)
  })
}

/**
 * 测试分割点选择
 */
function testSplitPointSelection() {
  console.log('\n=== 分割点选择测试 ===\n')
  
  const testText = 'pH值、溶解氧、化学需氧量、生化需氧量、氨氮、总磷、总氮'
  const maxChars = 20
  
  console.log(`原文本: "${testText}"`)
  console.log(`最大字符数: ${maxChars}`)
  
  // 查找分隔符
  const separators = ['、', '，', ',', ' ', '；', ';']
  const searchStart = Math.max(maxChars - 8, 0)
  const searchEnd = Math.min(maxChars - 3, testText.length - 3)
  
  console.log(`搜索范围: ${searchStart} - ${searchEnd}`)
  
  for (let i = searchEnd; i >= searchStart; i--) {
    const char = testText[i]
    if (separators.includes(char)) {
      console.log(`找到分隔符 "${char}" 在位置 ${i}`)
      console.log(`第一行: "${testText.substring(0, i + 1).trim()}"`)
      console.log(`第二行: "${testText.substring(i + 1).trim()}"`)
      break
    }
  }
}

// 运行测试
function runAllTests() {
  testDetectionMethodTwoLines()
  testCharacterWidth()
  testSplitPointSelection()
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testDetectionMethodTwoLines,
    testCharacterWidth,
    testSplitPointSelection,
    processDetectionMethod,
    runAllTests
  }

  // 如果直接运行此文件，执行测试
  if (require.main === module) {
    runAllTests()
  }
} else {
  // 在浏览器或小程序环境中直接运行
  runAllTests()
}
