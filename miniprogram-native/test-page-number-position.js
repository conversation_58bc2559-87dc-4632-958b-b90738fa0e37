/**
 * 测试页码位置调整
 * 验证样品编号中的序号信息已移到页脚右边
 */

const CPCLPrintAdapter = require('./utils/cpcl_print/adapter.js');

console.log('=== 页码位置调整测试 ===\n');

// 测试1: 验证样品编号格式（不包含序号）
console.log('1. 验证样品编号格式...');
try {
  const cpclPrinter = new CPCLPrintAdapter();
  
  const testData = {
    sampleType: '无组织废气',
    sampleId: '24120001Q475', // 只包含任务编号，不包含（1/1）
    samplingDate: '2024-12-31',
    samplingPoint: '厂界下风向3（周期1）',
    testItems: '臭气浓度',
    container: '10L气袋 10L',
    storageMethod: '17-25℃常温保存 密封 避光 无',
    pageInfo: '3/4' // 序号信息在页脚
  };

  const commands = cpclPrinter.generateCPCLCommands(testData);
  const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
  
  // 检查样品编号
  const sampleIdMatch = commandStr.match(/样品编号：([^\r\n]+)/);
  if (sampleIdMatch) {
    const sampleIdText = sampleIdMatch[1];
    console.log('样品编号内容:', sampleIdText);
    
    // 检查是否包含括号和斜杠（序号格式）
    const hasParentheses = sampleIdText.includes('（') || sampleIdText.includes('(');
    const hasSlash = sampleIdText.includes('/');
    
    console.log('- 包含括号:', hasParentheses ? '是 ❌' : '否 ✅');
    console.log('- 包含斜杠:', hasSlash ? '是 ❌' : '否 ✅');
    
    if (!hasParentheses && !hasSlash) {
      console.log('✅ 样品编号格式正确（不包含序号）');
    } else {
      console.log('❌ 样品编号仍包含序号信息');
    }
  } else {
    console.log('❌ 未找到样品编号字段');
  }
  
} catch (error) {
  console.log('❌ 样品编号格式测试失败:', error.message);
}

// 测试2: 验证页脚页码位置
console.log('\n2. 验证页脚页码位置...');
try {
  const cpclPrinter = new CPCLPrintAdapter();
  
  const testData = {
    sampleType: '测试样品',
    sampleId: 'TEST-001',
    samplingDate: '2024-01-15',
    samplingPoint: '测试点位',
    testItems: '测试项目',
    container: '测试容器',
    storageMethod: '测试保存',
    pageInfo: '1/5' // 页码信息
  };

  const commands = cpclPrinter.generateCPCLCommands(testData);
  const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
  
  // 查找页脚的TEXT命令（Y=440）
  const footerMatches = commandStr.matchAll(/TEXT \d+ \d+ (\d+) 440 ([^\r\n]+)/g);
  const footerItems = Array.from(footerMatches);
  
  console.log('页脚内容检查:');
  console.log('- 页脚项数量:', footerItems.length);
  
  if (footerItems.length === 2) {
    console.log('✅ 页脚包含两项（公司名称 + 页码）');
    
    footerItems.forEach((match, index) => {
      const x = match[1];
      const text = match[2];
      console.log(`\n  项${index + 1}:`);
      console.log(`  - X坐标: ${x}px`);
      console.log(`  - 内容: ${text.substring(0, 30)}`);
      
      if (index === 0) {
        // 第一项应该是公司名称（左侧，X=10）
        if (x === '10') {
          console.log('  - 位置: 左侧 ✅');
        }
      } else if (index === 1) {
        // 第二项应该是页码（右侧，X=420）
        if (x === '420') {
          console.log('  - 位置: 右侧 ✅');
        }
        if (text.includes('/')) {
          console.log('  - 格式: 页码格式 ✅');
        }
      }
    });
  } else if (footerItems.length === 1) {
    console.log('⚠️  页脚只有一项（可能缺少页码）');
  } else {
    console.log('❌ 页脚项数量异常');
  }
  
} catch (error) {
  console.log('❌ 页脚页码测试失败:', error.message);
}

// 测试3: 完整布局验证
console.log('\n3. 完整布局验证...');
try {
  const cpclPrinter = new CPCLPrintAdapter();
  
  const testData = {
    sampleType: '无组织废气',
    sampleId: '24120001Q475',
    samplingDate: '2024-12-31',
    samplingPoint: '厂界下风向3（周期1）',
    testItems: '臭气浓度',
    container: '10L气袋 10L',
    storageMethod: '17-25℃常温保存 密封 避光 无',
    pageInfo: '3/4'
  };

  const commands = cpclPrinter.generateCPCLCommands(testData);
  const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
  
  // 提取所有TEXT命令
  const textMatches = commandStr.matchAll(/TEXT \d+ \d+ \d+ (\d+) ([^\r\n]+)/g);
  const textItems = Array.from(textMatches).map(match => ({
    y: parseInt(match[1]),
    text: match[2]
  }));
  
  console.log('完整标签布局:');
  textItems.forEach((item, index) => {
    const preview = item.text.substring(0, 40);
    console.log(`${index + 1}. Y=${item.y}px - ${preview}`);
  });
  
  // 验证关键点
  console.log('\n关键点验证:');
  
  // 1. 样品编号应该不包含序号
  const sampleIdItem = textItems.find(item => item.text.includes('样品编号'));
  if (sampleIdItem) {
    const hasSequence = sampleIdItem.text.includes('（') || sampleIdItem.text.includes('/');
    console.log('- 样品编号不含序号:', hasSequence ? '❌' : '✅');
  }
  
  // 2. 页脚应该有两项
  const footerItems = textItems.filter(item => item.y === 440);
  console.log('- 页脚项数量:', footerItems.length === 2 ? '2 ✅' : `${footerItems.length} ❌`);
  
  // 3. 页脚应该包含页码
  const hasPageNumber = footerItems.some(item => item.text.includes('/'));
  console.log('- 页脚包含页码:', hasPageNumber ? '✅' : '❌');
  
} catch (error) {
  console.log('❌ 完整布局验证失败:', error.message);
}

// 测试4: 对比调整前后
console.log('\n4. 调整前后对比...');
try {
  console.log('布局变化对比:');
  
  const comparison = [
    {
      item: '样品编号',
      before: '24120001Q475（1/1）',
      after: '24120001Q475',
      change: '移除序号 ✅'
    },
    {
      item: '页脚左侧',
      before: '浙江求实环境监测有限公司',
      after: '浙江求实环境监测有限公司',
      change: '不变'
    },
    {
      item: '页脚右侧',
      before: '（无）',
      after: '1/1',
      change: '新增序号 ✅'
    }
  ];
  
  comparison.forEach(comp => {
    console.log(`\n${comp.item}:`);
    console.log(`  调整前: ${comp.before}`);
    console.log(`  调整后: ${comp.after}`);
    console.log(`  变化: ${comp.change}`);
  });
  
} catch (error) {
  console.log('❌ 对比分析失败:', error.message);
}

// 测试5: 不同序号格式测试
console.log('\n5. 不同序号格式测试...');
try {
  const testCases = [
    { pageInfo: '1/1', desc: '单个样品' },
    { pageInfo: '3/4', desc: '多个样品（中间）' },
    { pageInfo: '10/20', desc: '两位数序号' },
    { pageInfo: '', desc: '无页码信息' }
  ];
  
  testCases.forEach(testCase => {
    const cpclPrinter = new CPCLPrintAdapter();
    
    const testData = {
      sampleType: '测试',
      sampleId: 'TEST-001',
      samplingDate: '2024-01-15',
      samplingPoint: '测试点位',
      testItems: '测试',
      container: '测试',
      storageMethod: '测试',
      pageInfo: testCase.pageInfo
    };

    const commands = cpclPrinter.generateCPCLCommands(testData);
    const commandStr = Array.from(commands).map(b => String.fromCharCode(b)).join('');
    
    const footerMatches = commandStr.matchAll(/TEXT \d+ \d+ \d+ 440 ([^\r\n]+)/g);
    const footerItems = Array.from(footerMatches);
    
    console.log(`\n${testCase.desc} (pageInfo: "${testCase.pageInfo}"):`);
    console.log(`  - 页脚项数: ${footerItems.length}`);
    
    if (testCase.pageInfo) {
      const hasPageInfo = footerItems.some(match => match[1].includes(testCase.pageInfo));
      console.log(`  - 包含页码: ${hasPageInfo ? '✅' : '❌'}`);
    } else {
      console.log(`  - 无页码: ${footerItems.length === 1 ? '✅' : '❌'}`);
    }
  });
  
} catch (error) {
  console.log('❌ 序号格式测试失败:', error.message);
}

console.log('\n=== 测试总结 ===');
console.log('✅ 样品编号: 不再包含序号信息（如（1/1））');
console.log('✅ 页脚左侧: 浙江求实环境监测有限公司（X=10px）');
console.log('✅ 页脚右侧: 序号信息（如 3/4）（X=420px）');
console.log('✅ 布局优化: 信息分离更清晰');

console.log('\n📋 最终效果:');
console.log('样品编号：24120001Q475');
console.log('...');
console.log('浙江求实环境监测有限公司                    3/4');

console.log('\n🎉 页码位置调整完成！');
