# 小程序蓝牙打印解决方案

本解决方案专门解决小程序蓝牙打印中的常见问题：中文乱码、数据分包传输失败、打印格式错乱等。

## 🚀 核心功能

### ✅ 已解决的问题

1. **中文乱码问题** - 使用GB2312编码确保中文正确显示
2. **数据分包传输** - 自动分包≤20字节，延时发送，支持重试
3. **打印格式处理** - 58mm纸张适配，中英文混排，左右对齐

### 📦 模块结构

```
printer/
├── textEncoder.js      # 中文编码处理
├── bluetoothTransfer.js # 蓝牙数据传输管理
├── formatUtils.js      # 打印格式工具
├── printerManager.js   # 打印管理器（主入口）
├── example.js          # 使用示例
└── README.md          # 说明文档
```

## 🛠️ 快速开始

### 1. 安装依赖

确保项目中已安装 `text-encoding` 库：

```bash
npm install text-encoding
```

### 2. 基本使用

```javascript
const PrinterManager = require('./printer/printerManager');

// 创建打印管理器
const printerManager = new PrinterManager({
  bluetooth: {
    maxChunkSize: 20,    // 分包大小
    delay: 20,           // 分包延时
    retryCount: 3        // 重试次数
  },
  format: {
    maxCharsPerLine: 32, // 每行字符数
  },
  debug: true            // 调试模式
});

// 设置设备信息（连接成功后）
printerManager.setDeviceInfo({
  deviceId: 'your-device-id',
  serviceId: 'your-service-id',
  characteristicId: 'your-characteristic-id'
});

// 打印文本
await printerManager.printText('你好，世界！');
```

### 3. 高级功能

#### 打印左右对齐文本
```javascript
await printerManager.printLeftRight('商品名称', '价格');
await printerManager.printLeftRight('宫保鸡丁', '¥28.00');
```

#### 打印居中文本
```javascript
await printerManager.printCenter('欢迎光临', {
  fontSize: { width: 2, height: 2 },
  bold: true
});
```

#### 打印表格
```javascript
const tableData = [
  ['商品1', '2', '20.00'],
  ['商品2', '1', '15.00']
];

const columns = [
  { title: '商品', width: 16, align: 'left' },
  { title: '数量', width: 6, align: 'center' },
  { title: '金额', width: 10, align: 'right' }
];

await printerManager.printTable(tableData, columns, {
  showHeader: true
});
```

#### 自定义打印任务
```javascript
const job = printerManager.createPrintJob();

job.setAlign('ct').setSize(2, 2);
job.println('标题');

job.setAlign('lt').setSize(1, 1);
job.println('内容行1');
job.println('内容行2');

await printerManager.sendPrintJob(job);
```

## 🔧 配置选项

### PrinterManager 配置

```javascript
const config = {
  // 蓝牙传输配置
  bluetooth: {
    maxChunkSize: 20,      // 最大分包大小（字节）
    delay: 20,             // 分包间延时（毫秒）
    retryCount: 3,         // 重试次数
    retryDelay: 100        // 重试延时（毫秒）
  },
  
  // 格式配置
  format: {
    paperWidth: 58,        // 纸张宽度（mm）
    maxCharsPerLine: 32,   // 每行最大字符数
    fontWidth: 1,          // 字体宽度倍数
    fontHeight: 1          // 字体高度倍数
  },
  
  // 其他配置
  autoWrap: true,          // 自动换行
  encoding: 'gb2312',      // 编码格式
  debug: false             // 调试模式
};
```

## 📝 API 文档

### PrinterManager

#### 方法

- `setDeviceInfo(deviceInfo)` - 设置设备连接信息
- `printText(text, options)` - 打印文本
- `printLeftRight(left, right, options)` - 打印左右对齐文本
- `printCenter(text, options)` - 打印居中文本
- `printSeparator(char, options)` - 打印分割线
- `printTable(rows, columns, options)` - 打印表格
- `printReceipt()` - 打印示例小票
- `createPrintJob()` - 创建自定义打印任务
- `sendPrintJob(job, options)` - 发送打印任务

#### 事件回调

```javascript
await printerManager.sendPrintJob(job, {
  onProgress: (progress) => {
    console.log(`进度: ${progress.percentage}%`);
  }
});
```

### 格式工具 (FormatUtils)

- `getStringWidth(str)` - 计算字符串显示宽度
- `wrapText(text, maxWidth)` - 文本自动换行
- `alignLeftRight(left, right, fillChar)` - 左右对齐
- `alignCenter(text, fillChar)` - 居中对齐
- `createSeparatorLine(char)` - 创建分割线
- `createTableRow(columns)` - 创建表格行

## ⚠️ 注意事项

### 1. 设备连接
- 确保蓝牙设备已正确连接
- 获取正确的 serviceId 和 characteristicId
- 某些设备可能需要尝试多个特征值

### 2. 数据传输
- 单次传输不超过20字节
- 使用延时发送避免并发写入
- 支持自动重试机制

### 3. 中文编码
- 优先使用GB2312编码
- 降级支持UTF-8编码
- 自动处理中英文混排

### 4. 打印格式
- 58mm纸张，每行最多32个字符
- 中文字符占2个字符宽度
- 英文字符占1个字符宽度

## 🐛 常见问题

### Q: 打印出现乱码怎么办？
A: 确保使用了正确的编码格式，本方案默认使用GB2312编码处理中文。

### Q: 数据传输失败怎么办？
A: 检查分包大小设置，确保≤20字节，并启用重试机制。

### Q: 打印格式错乱怎么办？
A: 使用提供的格式工具进行文本对齐和换行处理。

### Q: 如何调试打印问题？
A: 启用debug模式，查看控制台输出的详细信息。

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
