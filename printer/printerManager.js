/**
 * 打印管理器 - 整合所有打印功能
 * 解决中文乱码、数据分包、格式处理等问题
 */

const PrinterJobs = require('./printerjobs');
const ChineseTextEncoder = require('./textEncoder');
const BluetoothTransfer = require('./bluetoothTransfer');
const PrintFormatUtils = require('./formatUtils');

class PrinterManager {
  constructor(options = {}) {
    this.textEncoder = new ChineseTextEncoder();
    this.bluetoothTransfer = new BluetoothTransfer(options.bluetooth);
    this.formatUtils = new PrintFormatUtils(options.format);
    
    // 设备连接信息
    this.deviceInfo = null;
    this.isConnected = false;
    
    // 打印配置
    this.config = {
      autoWrap: true,           // 自动换行
      encoding: 'gb2312',       // 编码格式
      debug: false,             // 调试模式
      ...options
    };
  }

  /**
   * 设置设备连接信息
   * @param {Object} deviceInfo - 设备信息
   */
  setDeviceInfo(deviceInfo) {
    this.deviceInfo = deviceInfo;
    this.isConnected = !!(deviceInfo && deviceInfo.deviceId);
    
    if (this.config.debug) {
      console.log('设备信息已设置:', deviceInfo);
    }
  }

  /**
   * 创建打印任务
   * @returns {PrinterJobs} 打印任务实例
   */
  createPrintJob() {
    const job = new PrinterJobs();
    
    // 重写text方法以支持中文编码
    const originalText = job.text.bind(job);
    job.text = (content) => {
      if (content) {
        const encoded = this.textEncoder.encode(content);
        job._enqueue(encoded);
      }
      return job;
    };
    
    return job;
  }

  /**
   * 打印文本（自动处理格式）
   * @param {string} text - 文本内容
   * @param {Object} options - 打印选项
   */
  async printText(text, options = {}) {
    if (!this.isConnected) {
      throw new Error('设备未连接');
    }

    const job = this.createPrintJob();
    
    // 应用格式选项
    if (options.align) {
      job.setAlign(options.align);
    }
    if (options.fontSize) {
      job.setSize(options.fontSize.width || 1, options.fontSize.height || 1);
    }
    if (options.bold) {
      job.setBold(true);
    }
    
    // 处理文本换行
    if (this.config.autoWrap && options.wrap !== false) {
      const lines = this.formatUtils.wrapText(text, options.maxWidth);
      lines.forEach(line => job.println(line));
    } else {
      job.println(text);
    }
    
    return this.sendPrintJob(job, options);
  }

  /**
   * 打印左右对齐文本
   * @param {string} leftText - 左侧文本
   * @param {string} rightText - 右侧文本
   * @param {Object} options - 选项
   */
  async printLeftRight(leftText, rightText, options = {}) {
    const formattedLine = this.formatUtils.alignLeftRight(
      leftText, 
      rightText, 
      options.fillChar || ' ',
      options.lineWidth
    );
    
    return this.printText(formattedLine, options);
  }

  /**
   * 打印居中文本
   * @param {string} text - 文本内容
   * @param {Object} options - 选项
   */
  async printCenter(text, options = {}) {
    const formattedText = this.formatUtils.alignCenter(
      text,
      options.fillChar || ' ',
      options.lineWidth
    );
    
    return this.printText(formattedText, { ...options, wrap: false });
  }

  /**
   * 打印分割线
   * @param {string} char - 分割字符
   * @param {Object} options - 选项
   */
  async printSeparator(char = '-', options = {}) {
    const separator = this.formatUtils.createSeparatorLine(char, options.lineWidth);
    return this.printText(separator, { ...options, wrap: false });
  }

  /**
   * 打印表格
   * @param {Array<Array>} rows - 表格数据
   * @param {Array<Object>} columns - 列配置
   * @param {Object} options - 选项
   */
  async printTable(rows, columns, options = {}) {
    if (!this.isConnected) {
      throw new Error('设备未连接');
    }

    const job = this.createPrintJob();
    
    // 打印表头（如果有）
    if (options.showHeader && columns.length > 0) {
      const headerColumns = columns.map(col => ({
        text: col.title || col.text || '',
        width: col.width,
        align: col.align || 'center'
      }));
      
      const headerRow = this.formatUtils.createTableRow(headerColumns, options.separator);
      job.println(headerRow);
      
      // 表头分割线
      if (options.headerSeparator !== false) {
        const separator = this.formatUtils.createSeparatorLine('-');
        job.println(separator);
      }
    }
    
    // 打印数据行
    rows.forEach(row => {
      const rowColumns = columns.map((col, index) => ({
        text: String(row[index] || ''),
        width: col.width,
        align: col.align || 'left'
      }));
      
      const formattedRow = this.formatUtils.createTableRow(rowColumns, options.separator);
      job.println(formattedRow);
    });
    
    return this.sendPrintJob(job, options);
  }

  /**
   * 发送打印任务
   * @param {PrinterJobs} job - 打印任务
   * @param {Object} options - 选项
   */
  async sendPrintJob(job, options = {}) {
    if (!this.isConnected) {
      throw new Error('设备未连接');
    }

    const buffer = job.buffer();
    
    if (this.config.debug) {
      console.log('打印数据:', this.bluetoothTransfer.bufferToHex(buffer));
    }
    
    return new Promise((resolve, reject) => {
      this.bluetoothTransfer.sendData(
        buffer,
        this.deviceInfo,
        options.onProgress,
        () => {
          if (this.config.debug) {
            console.log('打印完成');
          }
          resolve();
        },
        (error) => {
          console.error('打印失败:', error);
          reject(error);
        }
      );
    });
  }

  /**
   * 打印小票示例
   */
  async printReceipt() {
    if (!this.isConnected) {
      throw new Error('设备未连接');
    }

    const job = this.createPrintJob();
    
    // 店铺名称
    job.setAlign('ct').setSize(2, 2);
    job.println('美食餐厅');
    
    // 基本信息
    job.setAlign('ct').setSize(1, 1);
    job.println('欢迎光临！');
    job.println(new Date().toLocaleString());
    
    // 分割线
    job.println(this.formatUtils.createSeparatorLine());
    
    // 商品列表
    job.setAlign('lt');
    const items = [
      ['宫保鸡丁', '1', '28.00'],
      ['麻婆豆腐', '1', '18.00'],
      ['米饭', '2', '4.00']
    ];
    
    items.forEach(([name, qty, price]) => {
      const line = this.formatUtils.alignLeftRight(
        `${name} x${qty}`,
        `¥${price}`
      );
      job.println(line);
    });
    
    // 分割线
    job.println(this.formatUtils.createSeparatorLine());
    
    // 总计
    job.setAlign('rt');
    job.println('总计: ¥50.00');
    
    // 结尾
    job.setAlign('ct');
    job.println('谢谢惠顾！');
    job.lineFeed(3); // 空行
    
    return this.sendPrintJob(job);
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      deviceInfo: this.deviceInfo,
      transferStatus: this.bluetoothTransfer.getTransferStatus()
    };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.bluetooth) {
      this.bluetoothTransfer.updateConfig(newConfig.bluetooth);
    }
    
    if (newConfig.format) {
      this.formatUtils.updateConfig(newConfig.format);
    }
  }
}

module.exports = PrinterManager;
