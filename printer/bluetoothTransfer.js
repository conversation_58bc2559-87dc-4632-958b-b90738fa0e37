/**
 * 蓝牙数据传输管理模块
 * 解决蓝牙4.0单次传输限制问题
 */

class BluetoothTransfer {
  constructor(options = {}) {
    // 默认配置
    this.config = {
      maxChunkSize: 20,        // 最大分包大小（字节）
      delay: 20,               // 分包间延时（毫秒）
      retryCount: 3,           // 重试次数
      retryDelay: 100,         // 重试延时（毫秒）
      ...options
    };
    
    // 传输状态
    this.isTransferring = false;
    this.transferQueue = [];
    this.currentTransfer = null;
  }

  /**
   * 发送数据到蓝牙设备
   * @param {ArrayBuffer} buffer - 要发送的数据
   * @param {Object} deviceInfo - 设备信息
   * @param {Function} onProgress - 进度回调
   * @param {Function} onComplete - 完成回调
   * @param {Function} onError - 错误回调
   */
  async sendData(buffer, deviceInfo, onProgress, onComplete, onError) {
    if (this.isTransferring) {
      console.warn('正在传输数据，请等待当前传输完成');
      return;
    }

    this.isTransferring = true;
    
    try {
      // 数据分包
      const chunks = this.splitBuffer(buffer);
      console.log(`数据分包完成，共${chunks.length}包，总大小${buffer.byteLength}字节`);
      
      // 逐包发送
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const progress = {
          current: i + 1,
          total: chunks.length,
          percentage: Math.round(((i + 1) / chunks.length) * 100)
        };
        
        // 发送进度回调
        if (onProgress) {
          onProgress(progress);
        }
        
        // 发送数据包
        await this.sendChunk(chunk, deviceInfo, i);
        
        // 分包间延时
        if (i < chunks.length - 1) {
          await this.delay(this.config.delay);
        }
      }
      
      console.log('数据传输完成');
      if (onComplete) {
        onComplete();
      }
      
    } catch (error) {
      console.error('数据传输失败:', error);
      if (onError) {
        onError(error);
      }
    } finally {
      this.isTransferring = false;
    }
  }

  /**
   * 将ArrayBuffer分割成小包
   * @param {ArrayBuffer} buffer - 原始数据
   * @returns {Array<ArrayBuffer>} 分包后的数据数组
   */
  splitBuffer(buffer) {
    const chunks = [];
    const maxSize = this.config.maxChunkSize;
    
    for (let i = 0; i < buffer.byteLength; i += maxSize) {
      const end = Math.min(i + maxSize, buffer.byteLength);
      const chunk = buffer.slice(i, end);
      chunks.push(chunk);
    }
    
    return chunks;
  }

  /**
   * 发送单个数据包（带重试机制）
   * @param {ArrayBuffer} chunk - 数据包
   * @param {Object} deviceInfo - 设备信息
   * @param {number} index - 包序号
   */
  async sendChunk(chunk, deviceInfo, index) {
    let lastError = null;
    
    for (let retry = 0; retry < this.config.retryCount; retry++) {
      try {
        await this.writeBLECharacteristic(chunk, deviceInfo);
        console.log(`数据包${index + 1}发送成功 (${chunk.byteLength}字节)`);
        return; // 发送成功，退出重试循环
        
      } catch (error) {
        lastError = error;
        console.warn(`数据包${index + 1}发送失败，重试${retry + 1}/${this.config.retryCount}:`, error);
        
        if (retry < this.config.retryCount - 1) {
          await this.delay(this.config.retryDelay);
        }
      }
    }
    
    // 所有重试都失败
    throw new Error(`数据包${index + 1}发送失败: ${lastError.message}`);
  }

  /**
   * 写入蓝牙特征值
   * @param {ArrayBuffer} data - 要写入的数据
   * @param {Object} deviceInfo - 设备信息
   */
  writeBLECharacteristic(data, deviceInfo) {
    return new Promise((resolve, reject) => {
      wx.writeBLECharacteristicValue({
        deviceId: deviceInfo.deviceId,
        serviceId: deviceInfo.serviceId,
        characteristicId: deviceInfo.characteristicId,
        value: data,
        success: (res) => {
          resolve(res);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }

  /**
   * 延时函数
   * @param {number} ms - 延时毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 取消当前传输
   */
  cancelTransfer() {
    this.isTransferring = false;
    this.transferQueue = [];
    console.log('数据传输已取消');
  }

  /**
   * 获取传输状态
   */
  getTransferStatus() {
    return {
      isTransferring: this.isTransferring,
      queueLength: this.transferQueue.length
    };
  }

  /**
   * 设置传输配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('传输配置已更新:', this.config);
  }

  /**
   * 数据转换为十六进制字符串（调试用）
   * @param {ArrayBuffer} buffer - 数据缓冲区
   * @returns {string} 十六进制字符串
   */
  bufferToHex(buffer) {
    const hexArr = Array.prototype.map.call(
      new Uint8Array(buffer),
      function (bit) {
        return ('00' + bit.toString(16)).slice(-2);
      }
    );
    return hexArr.join(' ');
  }
}

module.exports = BluetoothTransfer;
